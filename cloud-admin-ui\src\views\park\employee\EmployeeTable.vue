<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate">添加岗亭员工</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template #default="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="success" v-if="scope.row.state === 0" @click="enabled(scope.row)"> 启用 </el-button>
            <el-button link type="danger" v-if="scope.row.state === 1" @click="disabled(scope.row)"> 禁用 </el-button>
            <el-button link type="warning" @click="handleCheckPsw(scope.row)"> 修改密码 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="员工账号" align="center" />
        <el-table-column prop="name" label="员工姓名" align="center" />
        <el-table-column prop="mobile" label="员工手机号" align="center" />
        <el-table-column prop="role_desc" label="角色类型" align="center" />
        <el-table-column prop="state_desc" label="启用状态" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog
        :title="data.form.id ? '编辑员工' : '添加员工'"
        v-model="createDialogVisible"
        :close-on-click-modal="false"
        @close="closeAddDialog(addForm)"
        width="500px"
      >
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item prop="username" label="员工账号">
            <el-input
              v-model="data.form.username"
              maxlength="10"
              :disabled="data.form.id"
              onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
              placeholder="请输入员工账号"
            />
          </el-form-item>
          <el-form-item prop="password" label="员工密码" v-if="!data.form.id">
            <el-input v-model="data.form.password" type="password" maxlength="10" show-password placeholder="请输入员工密码" />
          </el-form-item>
          <el-form-item prop="name" label="员工姓名">
            <el-input v-model="data.form.name" placeholder="请输入员工姓名" maxlength="10" />
          </el-form-item>
          <el-form-item prop="mobile" label="手机号"> <el-input v-model="data.form.mobile" placeholder="请输入手机号" /> </el-form-item>
          <el-form-item prop="role" label="角色">
            <el-select v-model="data.form.role" style="width: 100%" placeholder="请选择角色">
              <el-option v-for="item in roleList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="state" label="是否启用">
            <el-radio-group v-model="data.form.state">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createEmployee(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="查看授权通道" v-model="checkChannelDialogVisible" :close-on-click-modal="false" width="800px">
        <el-table :data="authorizationTableData" border>
          <el-table-column prop="region_name" label="子场名称" align="center" />
          <el-table-column prop="gateway_name" label="通道名称" align="center" />
          <el-table-column prop="longitude" label="经度" align="center" />
          <el-table-column prop="latitude" label="纬度" align="center" />
          <el-table-column prop="gateway_type_desc" label="通道类型" align="center" />
        </el-table>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="EmployeeTable" setup>
import { reactive, ref, onMounted, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parkEmployeeService from '@/service/park/ParkEmployeeService';
import commonService from '@/service/common/CommonService';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const addForm = ref();
const tableData = ref([]);
const loading = ref(false);
const roleList = ref([]);
const authorizationTableData = ref([]);
const total = ref(0);
const park_id = ref('');
const createDialogVisible = ref(false);
// 观察createDialogVisible的状态，重置表单
watch(createDialogVisible, (val) => {
  if (val === false) {
    data.form = {
      park_id: park_id.value,
      username: undefined,
      password: undefined,
      name: undefined,
      mobile: undefined,
      role: undefined,
      state: '1'
    };
    nextTick(() => {
      addForm.value.clearValidate();
    });
  }
});
const checkChannelDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  },
  form: {
    id: undefined,
    park_id: undefined,
    username: undefined,
    password: undefined,
    name: undefined,
    mobile: undefined,
    role: undefined,
    state: undefined
  },
  rules: {
    username: [
      {
        required: true,
        message: '请输入员工账号',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: '请输入员工密码',
        trigger: 'change'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入员工姓名',
        trigger: 'change'
      }
    ],
    mobile: [
      {
        required: true,
        message: '请输入手机号',
        trigger: 'change'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    role: [
      {
        required: true,
        message: '请选择角色',
        trigger: 'blur'
      }
    ],
    state: [
      {
        required: true,
        message: '请选择启用状态',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  initSelects();
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'roleList',
      enum_value: 'EnumSentryRole'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    roleList.value = response.data.roleList;
  });
};

// 分页查询员工列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  data.queryParams = params;
  parkEmployeeService.pagingParkAccount(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 启用
const enabled = (row) => {
  ElMessageBox.confirm('是否要启用该员工？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkEmployeeService.enableParkAccount(row.id).then(() => {
      ElMessage({
        message: '员工启用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};

// 禁用
const disabled = (row) => {
  ElMessageBox.confirm('是否要禁用该员工？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkEmployeeService.disableParkAccount(row.id).then(() => {
      ElMessage({
        message: '员工停用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};

// 添加员工
const handleCreate = () => {
  createDialogVisible.value = true;
};
// 编辑员工，账号和密码不可修改
const handleEdit = (row) => {
  data.form = {
    id: row.id,
    park_id: park_id.value,
    username: row.username,
    password: row.password,
    name: row.name,
    mobile: row.mobile,
    role: row.role,
    state: row.state + ''
  };
  createDialogVisible.value = true;
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建员工
const createEmployee = (addForm) => {
  addForm.validate().then(() => {
    let method = 'createParkAccount';
    if(data.form.id){
      method = 'updateParkAccountById';
    } else {
      method = 'createParkAccount'
    }
    parkEmployeeService
      .createParkAccount(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除员工
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkEmployeeService
      .deleteParkAccount(row)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
