<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div
          :style="{ color: CarTypeData.color[1], backgroundColor: CarTypeData.color[0] }"
          style="height: 30px; width: 100px; border-radius: 5px; display: flex; align-items: center; justify-content: center"
        >
          {{ CarTypeData.name }}
        </div>
      </el-space>
      <el-space>
        <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 340px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="100" fixed />
        <el-table-column label="时间周期" align="center" min-width="130" fixed>
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_id" label="车场id" align="center" min-width="100" fixed />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="100" fixed />
        <el-table-column prop="region_name" label="大区" align="center" min-width="100" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" min-width="100" />
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="100" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="100" />
        <el-table-column prop="district_name" label="所在区县" align="center" min-width="100" />
        <el-table-column prop="first_time" label="0-15分钟" align="center" min-width="130" />
        <el-table-column prop="first_money" label="0-15分钟金额" align="center" min-width="130" />
        <el-table-column prop="second_time" label="15分钟(不包含)-30分钟" align="center" min-width="130" />
        <el-table-column prop="second_money" label="15分钟(不包含)-30分钟金额" align="center" min-width="130" />
        <el-table-column prop="thirdly_time" label="30分钟(不包含)-1小时" align="center" min-width="130" />
        <el-table-column prop="thirdly_money" label="30分钟(不包含)-1小时金额" align="center" min-width="130" />
        <el-table-column prop="fourthly_time" label="1小时(不包含)-2小时" align="center" min-width="130" />
        <el-table-column prop="fourthly_money" label="1小时(不包含)-2小时金额" align="center" min-width="130" />
        <el-table-column prop="fifth_time" label="2小时(不包含)-3小时" align="center" min-width="130" />
        <el-table-column prop="fifth_money" label="2小时(不包含)-3小时金额" align="center" min-width="130" />
        <el-table-column prop="sixth_time" label="3小时(不包含)-4小时" align="center" min-width="130" />
        <el-table-column prop="sixth_money" label="3小时(不包含)-4小时金额" align="center" min-width="130" />
        <el-table-column prop="seventh_time" label="4小时(不包含)-5小时" align="center" min-width="130" />
        <el-table-column prop="seventh_money" label="4小时(不包含)-5小时金额" align="center" min-width="130" />
        <el-table-column prop="eighth_time" label="5小时(不包含)-6小时" align="center" min-width="130" />
        <el-table-column prop="eighth_money" label="5小时(不包含)-6小时金额" align="center" min-width="130" />
        <el-table-column prop="ninth_time" label="6小时(不包含)-7小时" align="center" min-width="130" />
        <el-table-column prop="ninth_money" label="6小时(不包含)-7小时金额" align="center" min-width="130" />
        <el-table-column prop="tenth_time" label="7小时(不包含)-8小时" align="center" min-width="130" />
        <el-table-column prop="tenth_money" label="7小时(不包含)-8小时金额" align="center" min-width="130" />
        <el-table-column prop="eleventh_time" label="8小时(不包含)-9小时" align="center" min-width="130" />
        <el-table-column prop="eleventh_money" label="8小时(不包含)-9小时金额" align="center" min-width="130" />
        <el-table-column prop="twelfth_time" label="9小时(不包含)-10小时" align="center" min-width="130" />
        <el-table-column prop="twelfth_money" label="9小时(不包含)-10小时金额" align="center" min-width="130" />
        <el-table-column prop="thirteenth_time" label="10小时以上" align="center" min-width="130" />
        <el-table-column prop="thirteenth_money" label="10小时以上金额" align="center" min-width="130" />
        <!-- <el-table-column prop="average_parking_time" label="日平均停车时长（小时）" align="center" /> -->
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkingTimeTable" setup>
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import parkingTimeService from '@/service/statisticalReport/ParkingTimeService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    time_type: '3',
    page: 1,
    limit: 30,
    car_type: '0'
  }
});
const newdata = ref();
onMounted(() => {
  getNewUpdateTmieData();
});
const CarTypeData = computed(() => {
  switch (data.queryParams.car_type) {
    case '0':
      return { name: '全部数据', color: ['#b3e0ff', '#005fa3'] }; // 更高对比度
    case '1':
      return { name: '临停数据', color: ['#b6f5bb', '#008a1a'] }; // 更高对比度
    case '2':
      return { name: '长租数据', color: ['#ffd6b8', '#c94a00'] }; // 更高对比度
    default:
      return { name: '全部数据', color: ['#b3e0ff', '#005fa3'] };
  }
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(13);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
const getList = (params) => {
  loading.value = true;
  if (params.car_type == '0') {
    data.queryParams = { ...data.queryParams, ...params, car_type: '' };
  } else {
    data.queryParams = { ...data.queryParams, ...params };
  }
  parkingTimeService.pagingParkingTime(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
