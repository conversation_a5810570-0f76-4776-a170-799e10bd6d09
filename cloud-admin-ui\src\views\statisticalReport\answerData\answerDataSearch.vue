<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.gateway_name" placeholder="通道名称"></el-input>
    </form-search-item>
    <form-search-item>
      <!-- <time-range
        v-model:date="form.dateRange"
        v-model:unit="form.dateType"
        style="width: 100%"
        :disabled-date="disabledDateFn"
        :show-type="['date']"
        type-value="type"
      /> -->
      <el-date-picker
        v-model="form.dateRange"
        type="date"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :disabled-date="disabledDateFn"
      />
    </form-search-item>
    <template #button>
      <DownloadButton btnType="success" :exportFunc="exportAnswerList" :params="{ ...form.queryParams, time_type }"> </DownloadButton>
      <!-- <DownloadButton
        btnType="warning"
        :exportFunc="exportAnswerSummary"
        :params="{ ...form.queryParams, time_type }"
        v-show="form.dateType != 'fiscalMonth'"
      >
        汇总导出</DownloadButton
      > -->
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>

  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_id="organization_ids"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="ParkingTimeSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import DownloadButton from '@/components/DownloadButton.vue';
import ParkingSpaceOccupationService from '@/service/statisticalReport/ParkingSpaceOccupationService';
import timeRange from '@/components/timeRange.vue';
import { exportAnswerList, exportAnswerSummary } from '@/api/answer/index.js'; //引入api接口
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import ParkFindBack from './ParkFindBack.vue';
import OrgFindBack from './OrgFindBack.vue';
import { reactive, ref, onMounted, watch } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
import { useUser } from '@/stores/user';
import { useRouter } from 'vue-router';
const router = useRouter();

const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const organization_ids = ref('');
const department_name = ref('');
const time_type = ref('3');
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    department_name: undefined,
    start_time: dayjs().format('YYYY-MM-DD'),
    end_time: dayjs().format('YYYY-MM-DD'),
    gateway_name: ''
  },
  dateRange: dayjs().format('YYYY-MM-DD'),
  dateType: 'date'
});
watch(
  () => form.dateType,
  () => {
    if (form.dateType == 'fiscalMonth') {
      console.log('caiwu ');
    }
  }
);
const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.queryParams.department_name = undefined;
};

const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};

onMounted(() => {
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  // if(user.role_id == 1){
  //     return false;
  // }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  const query = Object.assign(form.queryParams, {});
  emits('form-search', query);
});

const handleDataSearch = () => {
  if (form.dateRange) {
    form.queryParams.start_time = form.dateRange + ' 00:00:00';
    form.queryParams.end_time = form.dateRange + ' 23:59:59';
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  switch (form.dateType) {
    case 'date':
      time_type.value = '3';
      break;
    case 'week':
      time_type.value = '5';
      break;
    case 'month':
      time_type.value = '2';
      break;
    case 'fiscalMonth':
      time_type.value = '6';
      break;
    case 'year':
      time_type.value = '1';
      break;
    default: {
      time_type.value = '4';
    }
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { time_type: time_type.value });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    const query = Object.assign(form.queryParams, { time_type: time_type.value });
    emits('form-search', query);
  }
};
const handleAllReset = () => {
  form.dateRange = dayjs().format('YYYY-MM-DD');
  form.dateType = 'date';
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    start_time: undefined,
    end_time: undefined,
    time_type: '3'
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_ids.value = form.queryParams.organization_ids;
    department_name.value = form.queryParams.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_ids = arrId.toString();
  form.queryParams.department_name = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
