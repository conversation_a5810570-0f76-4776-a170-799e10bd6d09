<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <!-- <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div> -->
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 260px)">
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="park_id" label="车场id" align="center" />
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" />
        <el-table-column prop="openid" label="OpenId" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" />
        <el-table-column prop="pay_time" label="支付时间" align="center" />
        <el-table-column prop="pay_money" label="支付金额（元）" align="center" />
        <el-table-column prop="in_gateway_name" label="进场口" align="center" />
        <el-table-column prop="out_gateway_name" label="出场口" align="center" />
        <el-table-column prop="in_time" label="进场时间" align="center" />
        <el-table-column prop="to_time" label="出场时间" align="center" />
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="WholedayFrequencyTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import wholedayFrequencyService from '@/service/statisticalReport/WholedayFrequencyService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(17);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  wholedayFrequencyService.pagingAbnormalRecord(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
