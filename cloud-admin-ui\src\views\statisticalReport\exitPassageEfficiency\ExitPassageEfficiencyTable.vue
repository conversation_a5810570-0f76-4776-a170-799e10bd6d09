<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div class="search-btn-group" v-loading="countLoading">
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.midday_evening_payed_number_count }}</p>
            <span class="search-btn-group-total-label">午晚高峰付费车次</span>
          </div>
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.midday_evening_payed_average_pass_time_count }}</p>
            <span class="search-btn-group-total-label">午晚高峰付费车次平均通行时间（秒/车次）</span>
          </div>
        </div>
      </el-space>
      <el-space>
        <!-- <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div> -->
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 330px)">
        <el-table-column label="车场基础信息" align="center" fixed>
          <el-table-column label="统计日期" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间周期" align="center" min-width="70">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="60">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="50">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="region_name" label="大区" align="center" min-width="60" />
          <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="60" />
          <el-table-column prop="province_name" label="所在省份" align="center" min-width="60" />
          <el-table-column prop="city_name" label="所在城市" align="center" min-width="60" />
          <el-table-column prop="district_name" label="所在区域" align="center" min-width="60" />
          <!-- <el-table-column label="组织架构" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.organizational_structure }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="省市区" align="center" min-width="80">
            <template #default="scope">
              <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
                >{{ scope.row.province_name }}/</span
              >
              <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
                >{{ scope.row.city_name }}/</span
              >
              <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
                scope.row.district_name
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="全量车次周转率" align="center">
          <el-table-column label="周转率（含长租）" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.turnover_rate }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="全量临停数据" align="center">
          <el-table-column label="临停出场车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.parking_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停车次通行总时长(秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停车次平均通行时间(秒/车次）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="长租临停数据" align="center">
          <el-table-column label="长租出场车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长租车次通行总时长(秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长租车次平均通行时间(秒/车次）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="临停收费车次" align="center">
          <el-table-column label="临停付费车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.parking_payed_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停付费车次通行总时长(秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_payed_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停付费车次平均通行时间(秒/车次）" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.payed_average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column :render-header="renderHeader" label="午晚高峰期临停收费车次通行效率|12:00-15:00 午高峰|18:00-21:00 晚高峰" align="center">
          <el-table-column label="午晚高峰付费车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.midday_evening_payed_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="午晚高峰付费车次通行总时长（秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.midday_evening_payed_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="午晚高峰付费车次平均通行时间（秒/车次）" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.midday_evening_payed_average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="支付方式(加总约等于收费车次)" align="center">
          <el-table-column label="现金支付车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.cash_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="场内支付车次（场内c扫b）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.inside_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出口主动支付车次(出口c扫b，出口b扫c)" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.exit_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="无感支付车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.passive_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="etc支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etc_payment_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ExitPassageEfficiencyTable" setup>
import exitPassageEfficiencyService from '@/service/statisticalReport/ExitPassageEfficiencyService';
import { ElMessage } from 'element-plus';
import { h, reactive, ref, onMounted } from 'vue';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const countLoading = ref(false);
const countData = ref({
  midday_evening_payed_number_count: 0,
  midday_evening_payed_average_pass_time_count: 0
});
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(9);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
const renderHeader = ({ column, $index }) => {
  return h('span', {}, [
    h('span', {}, column.label.split('|')[0]),
    h('br'),
    h('span', {}, column.label.split('|')[1]),
    h('br'),
    h('span', {}, column.label.split('|')[2])
  ]);
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  exitPassageEfficiencyService.pagingExitPassageEfficiency(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  exitPassageEfficiencyService.parkExitTrafficEfficienciesCount(params).then((response) => {
    if (response.success === true) {
      if (response.data) {
        countData.value = response.data;
      }
      countLoading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      countLoading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}

.search-btn-group {
  gap: 4px;
}

.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  padding-top: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
