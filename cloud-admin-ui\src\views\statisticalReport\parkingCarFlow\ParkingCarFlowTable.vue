<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div class="search-btn-group" v-loading="countLoading">
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.car_in_number_count }}次</p>
            <span class="search-btn-group-total-label">入场车次</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.car_out_number_count }}次</p>
            <span class="search-btn-group-total-label">出场车次</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.parking_number_count }}次</p>
            <span class="search-btn-group-total-label">临停车次(出场)</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.rent_number_count }}次</p>
            <span class="search-btn-group-total-label">长租车次(出场)</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.free_number_count }}次</p>
            <span class="search-btn-group-total-label">临停免费车次</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.pay_parking_number_count }}次</p>
            <span class="search-btn-group-total-label">临停付费车次</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.average_parking_duration_count }}小时</p>
            <span class="search-btn-group-total-label">平均停车时长</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.parking_number_average_duration_count }}小时</p>
            <span class="search-btn-group-total-label">临停平均停车时长</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.rent_number_average_duration_count }}小时</p>
            <span class="search-btn-group-total-label">长租平均停车时长</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.parking_payed_number_average_duration_count }}小时</p>
            <span class="search-btn-group-total-label">临停付费平均停车时长</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.parking_free_number_average_duration_count }}小时</p>
            <span class="search-btn-group-total-label">临停免费平均停车时长</span>
          </div>
          &ensp;
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">
              {{ countData.day_turnover_rate_number_count }}
            </p>
            <span class="search-btn-group-total-label">平均日周转率</span>
          </div>
        </div>
      </el-space>
      <el-space>
        <!-- <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div> -->
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 450px)">
        <el-table-column label="车场基础信息" align="center" fixed>
          <el-table-column label="统计日期" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间周期" align="center" min-width="70">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="60">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="50">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="大区" align="center" min-width="60">
            <template #default="scope">
              <span>{{ scope.row.region_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="城市公司" align="center" min-width="60">
            <template #default="scope">
              <span>{{ scope.row.organizational_structure }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场省市区" align="center" min-width="80">
            <template #default="scope">
              <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
                >{{ scope.row.province_name }}/</span
              >
              <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
                >{{ scope.row.city_name }}/</span
              >
              <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
                scope.row.district_name
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否无人值守" align="center" min-width="60">
            <template #default="scope">
              <span>{{ scope.row.unattended_operation_desc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场类型" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_type_desc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.total_spaces }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="停车流量信息" align="center">
          <el-table-column label="入场车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.car_in_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出场车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.car_out_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="出场信息" align="center">
          <el-table-column label="长租车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="临停信息" align="center">
          <el-table-column label="免费车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.free_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="特殊处理车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.special_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.pay_parking_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="纯优免车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.coupon_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="未收车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.uncollected_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="付费临停信息" align="center">
          <el-table-column label="现金支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.cash_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="电子支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.electronic_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="现金且电子支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.electronic_and_cash_pay_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="临停大于30分钟小于24小时车次" align="center">
          <el-table-column label="有牌车" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.licensed_car_number }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="high_payment_number" label="高频支付车" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.high_payment_number || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="手动补录车" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.in_record_number || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="cash_payment_number" label="现金缴费车" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.cash_payment_number || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="无牌车" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.unlicensed_car_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="有牌车考核" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.thirty_licensed_car_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="燃油/新能源分类" align="center">
          <el-table-column label="燃油车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.fuel_car_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="燃油车牌（去重）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.fuel_car_number_distinct }}</span>
            </template>
          </el-table-column>
          <el-table-column label="新能源车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.new_energy_car_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="新能源车牌（去重）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.new_energy_car_number_distinct }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="特殊车辆信息" align="center">
          <el-table-column prop="hk_and_macao_in_count" label="港澳车入场车次" align="center" min-width="90"></el-table-column>
          <el-table-column prop="hk_and_macao_out_count" label="港澳车出场车次" align="center" min-width="90"></el-table-column>
          <el-table-column prop="hk_and_macao_count_proportion" label="港澳占比" align="center" min-width="90">
            <template #="{ row }"> {{ multiply(row.hk_and_macao_count_proportion, 100).toFixed(2) }}%</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="电子支付信息" align="center">
          <el-table-column label="微信万达小程序主动支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wx_wd_mini_program_initiative_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信万达小程序被动（无感）支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wx_wd_mini_program_passivity_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝万达小程序主动支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.ali_wd_mini_program_initiative_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝万达小程序被动（无感）支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.ali_wd_mini_program_passivity_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="万达通支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wd_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信无感支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_wx_passivity_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝无感支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_ali_passivity_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="ETC支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_etcpay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_wx_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_ali_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="复合支付" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_compound_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="其他支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_other_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序提前支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.hd_wx_mini_program_advanced_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序无感支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.hd_wx_mini_program_noninductive_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.hd_wx_scan_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-支付宝支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.hd_ali_scan_pay_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-ETC支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.hd_etc_scan_pay_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="各类电子支付占比分布" align="center">
          <el-table-column label="电子支付车次占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.electronic_pay_number_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序提前支付车次占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_wx_mini_program_advanced_pay_number_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序无感支付车次占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_wx_mini_program_noninductive_pay_number_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信支付车次占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_wx_scan_pay_number_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-支付宝支付车次占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_ali_scan_pay_number_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-ETC支付车次占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_etc_scan_pay_number_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="优免信息" align="center">
          <el-table-column label="纸质优免车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.paper_coupon_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="电子优免车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.electronic_coupon_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="扫码方式信息" align="center">
          <el-table-column label="出口B扫C车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.out_bscan_cnumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出口C扫B车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.out_cscan_bnumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="场内C扫B车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.inside_cscan_bnumber }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="停车车主规模" align="center">
          <el-table-column label="停车车牌数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_plate_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="日均及衍生指标" align="center">
          <el-table-column label="日均应收车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.average_daily_payed_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="日均电子支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.average_daily_electronic_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场日周转率" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.day_turnover_rate_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="平均停车时长(小时)" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.average_parking_duration }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长租车车次平均停车时长" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_number_average_duration }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停车次平均停车时长" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_number_average_duration }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停付费车次平均停车时长" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_payed_number_average_duration }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停免费车次平均停车时长" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_free_number_average_duration }}</span>
            </template>
          </el-table-column>
          <el-table-column label="应收车次或收费车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.receivable_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkingCarFlowTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import parkingCarFlowService from '@/service/statisticalReport/ParkingCarFlowService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
// import commonService from '@/service/common/CommonService';
// import { saveToFile } from '@/utils/utils.js';
import { multiply } from '@/utils/computeData';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const countLoading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const countData = ref({
  car_in_number_count: 0,
  car_out_number_count: 0,
  parking_number_count: 0,
  rent_number_count: 0,
  free_number_count: 0,
  pay_parking_number_count: 0,
  average_parking_duration_count: 0,
  parking_number_average_duration_count: 0,
  rent_number_average_duration_count: 0,
  parking_payed_number_average_duration_count: 0,
  parking_free_number_average_duration_count: 0,
  day_turnover_rate_number_count: 0
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(2);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
// getList(data.queryParams);
// });
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
const getList = (params) => {
  loading.value = true;
  console.log(params, 'params');
  data.queryParams = params;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  parkingCarFlowService.pagingParkingCarFlow(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  parkingCarFlowService.parkTrafficFlowsCount({ ...params, page: undefined, limit: undefined }).then((response) => {
    if (response.success === true) {
      if (response.data) {
        countData.value = response.data;
      }
      countLoading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      countLoading.value = false;
    }
  });
};
// const dayDownLoading = ref(false);
// const monthDownLoading = ref(false);
// // 按日导出
// const exportDataByDay = () => {
//   // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
//   //   ElMessage({
//   //     message: '请选择停车场进行统计',
//   //     type: 'warning'
//   //   });
//   //   return false;
//   // }
//   // if (data.queryParams.start_time !== undefined && data.queryParams.start_time !== '' && data.queryParams.start_time !== null) {
//   //   // 选择的时间最长只能是31天
//   //   var d = new Date(Date.parse(data.queryParams.start_time.replace(/-/g, '/')));
//   //   var d2 = new Date(Date.parse(data.queryParams.end_time.replace(/-/g, '/')));
//   //   var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
//   //   if (days + 1 > 31) {
//   //     ElMessage({
//   //       message: '查询日期最长只能选择31天！',
//   //       type: 'warning'
//   //     });
//   //     return false;
//   //   }
//   // }
//   parkingCarFlowService
//     .exportData(data.queryParams)
//     .then((response) => {
//       if (response.success == true) {
//         dayDownLoading.value = true;
//         commonService
//           .fileDownload(response.data)
//           .then((res) => {
//             let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
//             saveToFile(res.data, decodeURIComponent(fileName));
//             dayDownLoading.value = false;
//           })
//           .catch(() => {
//             dayDownLoading.value = false;
//           });
//       } else {
//         ElMessage({
//           message: response.detail_message != '' ? response.detail_message : response.message,
//           type: 'error'
//         });
//         dayDownLoading.value = false;
//       }
//     })
//     .catch(() => {
//       dayDownLoading.value = false;
//     });
// };
// // 按周导出
// const exportDataByWeek = () => {
//   if (
//     data.queryParams.start_time === undefined ||
//     data.queryParams.start_time === '' ||
//     data.queryParams.end_time === undefined ||
//     data.queryParams.end_time === ''
//   ) {
//     ElMessage({
//       message: '请选择统计日期！',
//       type: 'warning'
//     });
//     return false;
//   }
//   if (dayjs(data.queryParams.start_time).day() !== 1) {
//     ElMessage({
//       message: '请选择正确的周起始日期！',
//       type: 'warning'
//     });
//     return false;
//   }
//   if (dayjs(data.queryParams.end_time).day() !== 0) {
//     ElMessage({
//       message: '请选择正确的周结束日期！',
//       type: 'warning'
//     });
//     return false;
//   }
//   parkingCarFlowService.exportDataByMonth(data.queryParams).then((response) => {
//     if (response.success == true) {
//       monthDownLoading.value = true;
//       commonService
//         .fileDownload(response.data)
//         .then((res) => {
//           let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
//           saveToFile(res.data, decodeURIComponent(fileName));
//           monthDownLoading.value = false;
//         })
//         .catch(() => {
//           monthDownLoading.value = false;
//         });
//     } else {
//       ElMessage({
//         message: response.detail_message != '' ? response.detail_message : response.message,
//         type: 'error'
//       });
//     }
//   });
// };
// // 按月导出
// const exportDataByMonth = () => {
//   if (
//     data.queryParams.start_time === undefined ||
//     data.queryParams.start_time === '' ||
//     data.queryParams.end_time === undefined ||
//     data.queryParams.end_time === ''
//   ) {
//     ElMessage({
//       message: '请选择统计日期！',
//       type: 'warning'
//     });
//     return false;
//   }
//   // var startTime = (data.queryParams.start_time + '').slice(8);
//   // var endTime = data.queryParams.end_time;
//   // if (startTime !== '01') {
//   //   ElMessage({
//   //     message: '请选择正确的月首月尾！',
//   //     type: 'warning'
//   //   });
//   //   return false;
//   // }
//   // var s = new Date(data.queryParams.start_time.replace(/\-/, '/ '));
//   // var d = new Date(endTime.replace(/\-/, '/ '));
//   // var nd = new Date(d.getTime() + 24 * 60 * 60 * 1000); //next day
//   // if (s.getFullYear() !== d.getFullYear()) {
//   //   ElMessage({
//   //     message: '请选择正确的月首月尾！',
//   //     type: 'warning'
//   //   });
//   //   return false;
//   // }
//   // if (s.getMonth() !== d.getMonth()) {
//   //   ElMessage({
//   //     message: '请选择正确的月首月尾！',
//   //     type: 'warning'
//   //   });
//   //   return false;
//   // }
//   // if (d.getMonth() == nd.getMonth()) {
//   //   ElMessage({
//   //     message: '请选择正确的月首月尾！',
//   //     type: 'warning'
//   //   });
//   //   return false;
//   // }
//   parkingCarFlowService.exportDataByMonth(data.queryParams).then((response) => {
//     if (response.success == true) {
//       monthDownLoading.value = true;
//       commonService
//         .fileDownload(response.data)
//         .then((res) => {
//           let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
//           saveToFile(res.data, decodeURIComponent(fileName));
//           monthDownLoading.value = false;
//         })
//         .catch(() => {
//           monthDownLoading.value = false;
//         });
//     } else {
//       ElMessage({
//         message: response.detail_message != '' ? response.detail_message : response.message,
//         type: 'error'
//       });
//     }
//   });
// };

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.search-btn-group {
  gap: 4px 0px;
}
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  padding-top: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
</style>
