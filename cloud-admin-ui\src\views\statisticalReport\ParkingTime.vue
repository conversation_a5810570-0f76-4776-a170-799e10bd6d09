<template>
  <div class="container">
    <parking-time-search @form-search="searchParkingTimeList" @reset="resetParamsAndData" />
    <parking-time-table ref="table" />
  </div>
</template>

<script name="ParkingTime" setup>
import ParkingTimeSearch from './parkingTime/ParkingTimeSearch.vue';
import ParkingTimeTable from './parkingTime/ParkingTimeTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkingTimeList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
