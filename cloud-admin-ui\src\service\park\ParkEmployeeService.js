import * as employee from '@/api/park/ParkEmployeeApi';

/**
 * 车场-员工
 */
export default {
  /**
   * 分页查找员工信息
   */
  pagingParkAccount(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.pagingParkAccount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 员工信息保存
   */
  createParkAccount(data) {
    return employee.createParkAccount(data);
  },

  /**
   * 员工信息编辑
   */
  updateParkAccountById(data) {
    return employee.updateParkAccountById(data);
  },

  /**
   * 员工信息删除
   */
  deleteParkAccount(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.deleteParkAccount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用员工
   */
  enableParkAccount(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.enableParkAccount(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 禁用员工
   */
  disableParkAccount(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.disableParkAccount(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 分页查询员工授权通道
   */
  pagingAccountAuthorization(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.pagingAccountAuthorization(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询当前车场通道列表
   */
  listParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.listParkGateway(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通道授权
   */
  authorizationGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.authorizationGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询当前员工在当前车场已授权的通道列表
   */
  listAccountGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.listAccountGateway(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
