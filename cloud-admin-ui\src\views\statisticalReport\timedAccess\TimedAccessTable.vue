<template>
  <el-card class="table" shadow="never">
    <div style="display: flex; align-items: center; gap: 10px">
      <el-radio-group v-model="tabPosition">
        <el-radio-button value="列表">列表</el-radio-button>
        <el-radio-button value="图表">图表</el-radio-button>
      </el-radio-group>
      <div
        :style="{ color: CarTypeData.color[1], backgroundColor: CarTypeData.color[0] }"
        style="height: 30px; width: 100px; border-radius: 5px; display: flex; align-items: center; justify-content: center"
      >
        {{ CarTypeData.name }}
      </div>
    </div>
    <div v-if="tabPosition == '列表'" ref="table">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <!-- <div class="uodataClass">
            <el-tooltip>
              <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
              <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
            </el-tooltip>
            <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
          </div> -->
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 340px)">
        <el-table-column label="车场基础信息" align="center" fixed>
          <el-table-column label="统计日期" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间周期" align="center" min-width="80">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="60">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="大区" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.region_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="城市分公司" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.organizational_structure }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所在省份" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.province_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所在城市" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.city_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所在区域" align="center" min-width="70">
            <template #default="scope">
              <span>{{ scope.row.district_name }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="00:00-01:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.zero_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.zero_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="01:00-02:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.one_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.one_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="02:00-03:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.two_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.two_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="03:00-04:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.three_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.three_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="04:00-05:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.four_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.four_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="05:00-06:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.five_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.five_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="06:00-07:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.six_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.six_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="07:00-08:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.seven_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.seven_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="08:00-09:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.eight_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.eight_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="09:00-10:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.nine_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.nine_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="10:00-11:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ten_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.ten_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="11:00-12:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.eleven_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.eleven_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="12:00-13:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twelve_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twelve_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="13:00-14:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.thirteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.thirteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="14:00-15:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.fourteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.fourteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="15:00-16:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.fifteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.fifteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="16:00-17:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.sixteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.sixteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="17:00-18:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.seventeen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.seventeen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="18:00-19:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.eighteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.eighteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="19:00-20:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.nineteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.nineteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="20:00-21:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="21:00-22:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_one_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_one_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="22:00-23:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_two_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_two_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="23:00-24:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_three_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_three_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="tabPosition == '图表' && parkId">
      <echartbar :tableData="tableData" :title="data.queryParams.park_name"></echartbar>
    </div>
    <div v-if="tabPosition == '图表' && !parkId">
      <div style="width: 100%; height: 600px; background: #fff; line-height: 600px; text-align: center">请选择停车场进行统计</div>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="TimedAccessTable" setup>
import timedAccessService from '@/service/statisticalReport/TimedAccessService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';

import { ElMessage } from 'element-plus';
import { reactive, ref, onMounted, computed } from 'vue';
import echartbar from './barChart.vue';
const tableData = ref([]);
const loading = ref(false);
const tabPosition = ref('列表');
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    car_type: '0'
  }
});
const parkId = ref(null);
// onMounted(() => {
//   getList(data.queryParams);
// });
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
const CarTypeData = computed(() => {
  switch (data.queryParams.car_type) {
    case '0':
      return { name: '全部数据', color: ['#b3e0ff', '#005fa3'] }; // 更高对比度
    case '1':
      return { name: '临停数据', color: ['#b6f5bb', '#008a1a'] }; // 更高对比度
    case '2':
      return { name: '长租数据', color: ['#ffd6b8', '#c94a00'] }; // 更高对比度
    default:
      return { name: '全部数据', color: ['#b3e0ff', '#005fa3'] };
  }
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(15);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
const getList = (params) => {
  loading.value = true;
  if (params.car_type == '0') {
    data.queryParams = { ...data.queryParams, ...params, car_type: '' };
  } else {
    data.queryParams = { ...data.queryParams, ...params };
  }
  timedAccessService.pagingTimedAccess(data.queryParams).then((response) => {
    if (response.success === true) {
      parkId.value = data.queryParams.park_id;
      total.value = Number(response.data.total);
      tableData.value = response.data.rows;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
