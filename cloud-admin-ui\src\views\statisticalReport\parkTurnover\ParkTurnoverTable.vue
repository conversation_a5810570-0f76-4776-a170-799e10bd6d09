<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never" class="table-warp">
    <div class="opers" v-show="props.type === 'date'">
      <el-space>
        <div class="search-btn-group" v-loading="countLoading">
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.average_turnround_rate_count }}</p>
            <span class="search-btn-group-total-label">总平均日周转率</span>
          </div>
        </div>
      </el-space>
      <el-space>
        <!-- <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div> -->
      </el-space>
    </div>
    <!-- <div class="uodataClass" v-show="props.type !== 'date'">
      <el-tooltip>
        <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
        <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
      </el-tooltip>
      <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
    </div> -->
    <div ref="table">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        :style="{ height: props.type == 'date' ? 'calc(100vh - 395px)' : 'calc(100vh - 315px)' }"
      >
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column label="时间周期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="turnround_rate_rank" label="排名" align="center" />
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" width="100" />
        <!-- <el-table-column prop="organizational_structure" label="组织架构" align="center" /> -->
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="province_name" label="所在省份" align="center" />
        <el-table-column prop="city_name" label="所在城市" align="center" />
        <el-table-column prop="district_name" label="所在区域" align="center" />
        <el-table-column prop="parking_out_number" label="总临停车次" align="center" />
        <el-table-column prop="parking_payed_out_number" label="付费临停出场车次数" align="center" />
        <el-table-column prop="space_number" label="总车位数" align="center" />
        <el-table-column prop="average_turnround_rate" :label="type == 'date' ? '总平均日周转率' : '总平均月周转率'" align="center" />
        <el-table-column prop="payed_average_turnround_rate" :label="type == 'date' ? '付费平均日周转率' : '付费平均月周转率'" align="center" />
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkTurnoverTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import parkTurnoverService from '@/service/statisticalReport/ParkTurnoverService';
import { findTurnroundRatesCount } from '@/api/statisticalReport/ParkTurnoverApi';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const tableData = ref([]);
const loading = ref(false);
const countData = ref({
  average_turnround_rate_count: 0
});
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const props = defineProps({
  type: String
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(3);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  if (props.type == 'date') {
    return '星期' + week[new Date(row.statistics_date).getDay()];
  } else {
    return row.statistics_date.split('-')[1] + '月';
  }
};
const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  const time_type = props.type === 'date' ? '3' : '2';
  findTurnroundRatesCount({ ...data.queryParams, time_type }).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });

  parkTurnoverService.turnroundRatesCount({ ...data.queryParams, time_type }).then((response) => {
    if (response.success === true) {
      countData.value = response.data;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}

.pagination {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
