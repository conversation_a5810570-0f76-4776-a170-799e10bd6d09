<template>
  <div class="container">
    <timed-access-search @form-search="searchTimedAccessList" @reset="resetParamsAndData" />
    <timed-access-table ref="table" />
  </div>
</template>

<script name="TimedAccess" setup>
import TimedAccessSearch from './timedAccess/TimedAccessSearch.vue';
import TimedAccessTable from './timedAccess/TimedAccessTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchTimedAccessList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
<style scoped lang="scss">
.container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
